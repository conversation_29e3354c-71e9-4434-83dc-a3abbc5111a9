version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: freshShop-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-freshShop123}
      MYSQL_DATABASE: ${DB_NAME:-freshShop_prod}
      MYSQL_USER: ${DB_USERNAME:-freshShop}
      MYSQL_PASSWORD: ${DB_PASSWORD:-freshShop123}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - freshShop-network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # FreshShop Application
  app:
    build: .
    container_name: freshShop-app
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-prod}
      DATABASE_URL: ***********************/${DB_NAME:-freshShop_prod}?useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
      DB_USERNAME: ${DB_USERNAME:-freshShop}
      DB_PASSWORD: ${DB_PASSWORD:-freshShop123}
      SERVER_PORT: 8080
      JAVA_OPTS: "-Xms512m -Xmx1024m -XX:+UseG1GC"
      LOG_PATH: /app/logs
    ports:
      - "8080:8080"
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - freshShop-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: freshShop-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - freshShop-network
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: freshShop-nginx
    restart: unless-stopped
    depends_on:
      - app
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    networks:
      - freshShop-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  nginx_logs:
    driver: local

networks:
  freshShop-network:
    driver: bridge
