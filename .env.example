# FreshShop Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Configuration
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080

# Database Configuration
DB_NAME=freshShop_prod
DB_USERNAME=freshShop
DB_PASSWORD=freshShop123
DB_ROOT_PASSWORD=freshShop123
DATABASE_URL=*******************************************************************************************************

# Security Configuration
COOKIE_SECURE=true
SSL_ENABLED=false
SSL_KEYSTORE_PATH=
SSL_KEYSTORE_PASSWORD=

# Logging Configuration
LOG_PATH=/var/log/freshShop
LOG_LEVEL=INFO

# File Upload Configuration
UPLOAD_PATH=/app/uploads
TEMP_DIR=/tmp

# Cache Configuration
CACHE_TYPE=caffeine
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Monitoring Configuration
PROMETHEUS_ENABLED=false
FLYWAY_ENABLED=false

# Email Configuration (if needed)
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_FROM=<EMAIL>

# External API Configuration (if needed)
PAYMENT_API_KEY=
PAYMENT_API_SECRET=

# Docker Configuration
COMPOSE_PROJECT_NAME=freshShop

# Development specific (only for dev profile)
SHOW_SQL=false
DDL_AUTO=validate

# Production specific
JAVA_OPTS=-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication
