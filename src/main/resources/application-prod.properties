# Production Environment Configuration
# FreshShop Application

# Database configuration for production
spring.datasource.url=${DATABASE_URL:******************************************************************************************************}
spring.datasource.username=${DB_USERNAME}
spring.datasource.password=${DB_PASSWORD}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA and Hibernate settings for production
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.open-in-view=false

# Hibernate performance settings for production
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.properties.hibernate.cache.use_second_level_cache=true
spring.jpa.properties.hibernate.cache.use_query_cache=true
spring.jpa.properties.hibernate.cache.region.factory_class=org.hibernate.cache.jcache.JCacheRegionFactory

# Connection pool configuration for production
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.pool-name=HikariPool-FreshShop-Prod
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.leak-detection-threshold=60000

# Logging configuration for production
logging.level.vn.fshop=INFO
logging.level.org.springframework.security=WARN
logging.level.org.springframework.web=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.level.org.springframework.cache=WARN

# File logging for production
logging.file.name=${LOG_PATH:/var/log/freshShop}/freshShop-prod.log
logging.file.max-size=100MB
logging.file.max-history=30
logging.file.total-size-cap=1GB

# Logging pattern for production
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} %-5level %logger{36} - %msg%n

# Server settings for production
server.port=${PORT:8080}
server.error.include-message=never
server.error.include-binding-errors=never
server.error.include-stacktrace=never
server.error.include-exception=false

# Compression for production
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# Session configuration for production
server.servlet.session.timeout=30m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=${COOKIE_SECURE:true}
server.servlet.session.cookie.same-site=strict
server.servlet.session.tracking-modes=cookie

# File upload settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true
spring.servlet.multipart.location=${TEMP_DIR:/tmp}

# Cache configuration for production
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=300s
spring.cache.cache-names=categories,products,productsByCategory,productImages,userProfiles,productStats,orderStats

# Actuator endpoints for production (limited exposure)
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.endpoint.health.show-components=when-authorized
management.info.env.enabled=false

# Health check configuration
management.health.db.enabled=true
management.health.diskspace.enabled=true
management.health.diskspace.threshold=1GB

# Application info
info.app.name=FreshShop
info.app.description=Fresh Fruit E-commerce Application
info.app.version=@project.version@
info.app.environment=production

# Security settings for production
spring.security.debug=false

# Thymeleaf settings for production
spring.thymeleaf.cache=true
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Static resources for production
spring.web.resources.cache.period=31536000
spring.web.resources.chain.cache=true
spring.web.resources.chain.compressed=true
spring.web.resources.chain.strategy.content.enabled=true
spring.web.resources.chain.strategy.content.paths=/**
spring.web.resources.chain.strategy.fixed.enabled=true
spring.web.resources.chain.strategy.fixed.paths=/js/**,/css/**
spring.web.resources.chain.strategy.fixed.version=v1.0

# SSL/TLS configuration for production
server.ssl.enabled=${SSL_ENABLED:false}
server.ssl.key-store=${SSL_KEYSTORE_PATH:}
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD:}
server.ssl.key-store-type=PKCS12

# Additional security headers
server.servlet.session.cookie.max-age=1800
server.forward-headers-strategy=native

# Database migration settings
spring.flyway.enabled=${FLYWAY_ENABLED:false}
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true

# Monitoring and metrics
management.metrics.export.prometheus.enabled=${PROMETHEUS_ENABLED:false}
management.metrics.distribution.percentiles-histogram.http.server.requests=true
management.metrics.distribution.percentiles.http.server.requests=0.5,0.95,0.99
management.metrics.distribution.sla.http.server.requests=50ms,100ms,200ms,300ms,500ms,1s
