# Development Environment Configuration
# FreshShop Application

# Database configuration for development
spring.datasource.url=******************************************************************************************************
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA and Hibernate settings for development
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.ddl-auto=update
spring.jpa.open-in-view=false

# Hibernate performance settings
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.jdbc.batch_versioned_data=true
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false

# Connection pool configuration for development
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.pool-name=HikariPool-FreshShop-Dev
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.leak-detection-threshold=60000

# Logging configuration for development
logging.level.vn.fshop=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.cache=DEBUG

# Console logging pattern for development
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# File logging for development
logging.file.name=logs/freshShop-dev.log
logging.file.max-size=50MB
logging.file.max-history=7

# Server settings for development
server.port=${PORT:8080}
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=always
server.error.include-exception=true

# Session configuration for development
server.servlet.session.timeout=60m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false
server.servlet.session.cookie.same-site=lax

# File upload settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
spring.servlet.multipart.enabled=true

# Cache configuration for development
spring.cache.type=simple
spring.cache.cache-names=categories,products,productsByCategory,productImages,userProfiles,productStats,orderStats

# Development specific settings
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true
spring.h2.console.enabled=false

# Actuator endpoints for development monitoring
management.endpoints.web.exposure.include=health,info,metrics,env,configprops,beans,mappings
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always
management.info.env.enabled=true

# Application info
info.app.name=FreshShop
info.app.description=Fresh Fruit E-commerce Application
info.app.version=@project.version@
info.app.encoding=@project.build.sourceEncoding@
info.app.java.version=@java.version@
info.app.environment=development

# Security settings for development
spring.security.debug=true

# Thymeleaf settings for development
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Static resources for development
spring.web.resources.cache.period=0
spring.web.resources.chain.cache=false
