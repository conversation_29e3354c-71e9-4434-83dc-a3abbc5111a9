/**
 * Build Optimization and Resource Management
 * FreshShop Application
 */

const BuildOptimization = {
    // Resource loading optimization
    resources: {
        // Preload critical resources
        preloadCriticalResources() {
            const criticalResources = [
                { href: '/css/bootstrap.min.css', as: 'style' },
                { href: '/css/style.css', as: 'style' },
                { href: '/js/jquery-3.2.1.min.js', as: 'script' },
                { href: '/js/bootstrap.min.js', as: 'script' }
            ];
            
            criticalResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource.href;
                link.as = resource.as;
                if (resource.as === 'style') {
                    link.onload = function() {
                        this.onload = null;
                        this.rel = 'stylesheet';
                    };
                }
                document.head.appendChild(link);
            });
        },
        
        // Lazy load non-critical resources
        lazyLoadResources() {
            // Lazy load images
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        img.setAttribute('data-loaded', 'true');
                        observer.unobserve(img);
                    }
                });
            });
            
            images.forEach(img => imageObserver.observe(img));
            
            // Lazy load non-critical scripts
            this.lazyLoadScripts();
        },
        
        // Lazy load scripts
        lazyLoadScripts() {
            const nonCriticalScripts = [
                '/js/codeQuality.js'
            ];
            
            // Load after main content is ready
            window.addEventListener('load', () => {
                setTimeout(() => {
                    nonCriticalScripts.forEach(src => {
                        const script = document.createElement('script');
                        script.src = src;
                        script.async = true;
                        document.body.appendChild(script);
                    });
                }, 1000);
            });
        }
    },
    
    // Cache management
    cache: {
        // Service worker registration for caching
        registerServiceWorker() {
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/sw.js')
                        .then(registration => {
                            console.log('SW registered: ', registration);
                        })
                        .catch(registrationError => {
                            console.log('SW registration failed: ', registrationError);
                        });
                });
            }
        },
        
        // Local storage cache for API responses
        cacheApiResponse(key, data, ttl = 300000) { // 5 minutes default
            const cacheData = {
                data: data,
                timestamp: Date.now(),
                ttl: ttl
            };
            try {
                localStorage.setItem(`cache_${key}`, JSON.stringify(cacheData));
            } catch (error) {
                console.warn('Failed to cache data:', error);
            }
        },
        
        // Get cached API response
        getCachedApiResponse(key) {
            try {
                const cached = localStorage.getItem(`cache_${key}`);
                if (cached) {
                    const cacheData = JSON.parse(cached);
                    if (Date.now() - cacheData.timestamp < cacheData.ttl) {
                        return cacheData.data;
                    } else {
                        localStorage.removeItem(`cache_${key}`);
                    }
                }
            } catch (error) {
                console.warn('Failed to get cached data:', error);
            }
            return null;
        },
        
        // Clear expired cache
        clearExpiredCache() {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('cache_')) {
                    try {
                        const cached = localStorage.getItem(key);
                        const cacheData = JSON.parse(cached);
                        if (Date.now() - cacheData.timestamp >= cacheData.ttl) {
                            localStorage.removeItem(key);
                        }
                    } catch (error) {
                        localStorage.removeItem(key);
                    }
                }
            });
        }
    },
    
    // Performance optimization
    performance: {
        // Optimize images
        optimizeImages() {
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                // Add loading="lazy" if not present
                if (!img.hasAttribute('loading')) {
                    img.setAttribute('loading', 'lazy');
                }
                
                // Add proper alt text if missing
                if (!img.hasAttribute('alt')) {
                    img.setAttribute('alt', '');
                }
                
                // Optimize image loading
                img.addEventListener('load', function() {
                    this.style.opacity = '1';
                });
                
                img.addEventListener('error', function() {
                    this.style.display = 'none';
                    console.warn('Failed to load image:', this.src);
                });
            });
        },
        
        // Debounce scroll events
        optimizeScrollEvents() {
            let ticking = false;
            
            function updateScrollPosition() {
                // Handle scroll-dependent operations here
                ticking = false;
            }
            
            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateScrollPosition);
                    ticking = true;
                }
            }
            
            window.addEventListener('scroll', requestTick);
        },
        
        // Optimize form submissions
        optimizeFormSubmissions() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitButton = this.querySelector('button[type="submit"], input[type="submit"]');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.classList.add('loading');
                        
                        // Re-enable after 5 seconds as fallback
                        setTimeout(() => {
                            submitButton.disabled = false;
                            submitButton.classList.remove('loading');
                        }, 5000);
                    }
                });
            });
        }
    },
    
    // Bundle optimization
    bundle: {
        // Remove unused CSS
        removeUnusedCSS() {
            // This would typically be done at build time
            // Here we just log what could be optimized
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
            console.log(`Found ${stylesheets.length} stylesheets that could be optimized`);
        },
        
        // Minify inline scripts
        optimizeInlineScripts() {
            const scripts = document.querySelectorAll('script:not([src])');
            console.log(`Found ${scripts.length} inline scripts that could be optimized`);
        }
    },
    
    // Initialize optimization
    init() {
        console.log('Build optimization initialized');
        
        // Preload critical resources
        this.resources.preloadCriticalResources();
        
        // Setup lazy loading
        this.resources.lazyLoadResources();
        
        // Register service worker for caching
        this.cache.registerServiceWorker();
        
        // Clear expired cache
        this.cache.clearExpiredCache();
        
        // Optimize performance
        this.performance.optimizeImages();
        this.performance.optimizeScrollEvents();
        this.performance.optimizeFormSubmissions();
        
        // Run bundle optimization checks in development
        if (window.location.hostname === 'localhost') {
            this.bundle.removeUnusedCSS();
            this.bundle.optimizeInlineScripts();
        }
        
        // Expose for debugging
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            window.BuildOptimization = this;
        }
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => BuildOptimization.init());
} else {
    BuildOptimization.init();
}
