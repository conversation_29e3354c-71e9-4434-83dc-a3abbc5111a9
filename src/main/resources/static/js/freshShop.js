/**
 * FreshShop - Modern JavaScript Module
 * Optimized for performance and maintainability
 */

const FreshShop = {
    // Configuration
    config: {
        apiEndpoints: {
            addToCart: '/add-item',
            removeFromCart: '/remove-item-cart',
            clearCart: '/clear-cart',
            cartCount: '/cart-count',
            cartItems: '/api/cart/items'
        },
        debounceDelay: 300,
        notificationDuration: 5000
    },

    // Utility functions
    utils: {
        // Debounce function for performance
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Format currency
        formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        },

        // Validate email
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        // Validate phone number (Vietnamese format)
        isValidPhone(phone) {
            const phoneRegex = /^(\+84|84|0)[1-9][0-9]{8}$/;
            return phoneRegex.test(phone.replace(/\s/g, ''));
        },

        // Sanitize HTML to prevent XSS
        sanitizeHtml(str) {
            const temp = document.createElement('div');
            temp.textContent = str;
            return temp.innerHTML;
        }
    },

    // Notification system
    notifications: {
        show(message, type = 'info', duration = FreshShop.config.notificationDuration) {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.fresh-notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible fade show position-fixed fresh-notification`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);';
            
            const iconMap = {
                success: 'fa-check-circle',
                danger: 'fa-exclamation-triangle',
                warning: 'fa-exclamation-circle',
                info: 'fa-info-circle'
            };
            
            notification.innerHTML = `
                <i class="fa ${iconMap[type] || iconMap.info}"></i>
                ${FreshShop.utils.sanitizeHtml(message)}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after duration
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 150);
                }
            }, duration);
        },

        success(message) {
            this.show(message, 'success');
        },

        error(message) {
            this.show(message, 'danger');
        },

        warning(message) {
            this.show(message, 'warning');
        },

        info(message) {
            this.show(message, 'info');
        }
    },

    // Cart management
    cart: {
        // Add item to cart with enhanced error handling
        async addItem(productId, quantity = 1, cumulative = true) {
            try {
                // Validate inputs
                if (!productId || quantity <= 0) {
                    FreshShop.notifications.error('Thông tin sản phẩm không hợp lệ');
                    return false;
                }

                // Show loading state
                const button = document.querySelector(`[data-product-id="${productId}"]`);
                if (button) {
                    button.disabled = true;
                    const originalText = button.innerHTML;
                    button.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Đang thêm...';
                    
                    // Restore button after operation
                    setTimeout(() => {
                        button.disabled = false;
                        button.innerHTML = originalText;
                    }, 2000);
                }

                const response = await fetch(FreshShop.config.apiEndpoints.addToCart, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `idP=${productId}&quan=${quantity}&cumulative=${cumulative}`
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.text();
                FreshShop.notifications.success('Đã thêm sản phẩm vào giỏ hàng');
                
                // Update cart UI
                await FreshShop.cart.updateBadge();
                FreshShop.cart.refreshDropdown();
                
                return true;
            } catch (error) {
                console.error('Error adding item to cart:', error);
                FreshShop.notifications.error('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng');
                return false;
            }
        },

        // Update cart badge
        async updateBadge() {
            try {
                const response = await fetch(FreshShop.config.apiEndpoints.cartCount);
                const data = await response.json();
                
                const badge = document.querySelector('.cart-badge, .badge-cart');
                if (badge && data.success) {
                    badge.textContent = data.count;
                    badge.style.display = data.count > 0 ? 'inline' : 'none';
                }
            } catch (error) {
                console.error('Error updating cart badge:', error);
            }
        },

        // Refresh cart dropdown
        refreshDropdown() {
            if (typeof loadCartItems === 'function') {
                loadCartItems();
            }
        }
    },

    // Form validation
    validation: {
        // Real-time form validation
        setupRealTimeValidation(formSelector) {
            const form = document.querySelector(formSelector);
            if (!form) return;

            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', FreshShop.utils.debounce(() => this.validateField(input), 500));
            });
        },

        validateField(field) {
            const value = field.value.trim();
            const fieldName = field.name;
            let isValid = true;
            let message = '';

            // Remove existing error styling
            field.classList.remove('is-invalid', 'is-valid');
            const existingFeedback = field.parentNode.querySelector('.invalid-feedback');
            if (existingFeedback) existingFeedback.remove();

            // Required field validation
            if (field.hasAttribute('required') && !value) {
                isValid = false;
                message = 'Trường này là bắt buộc';
            }
            // Email validation
            else if (fieldName === 'email' && value && !FreshShop.utils.isValidEmail(value)) {
                isValid = false;
                message = 'Email không hợp lệ';
            }
            // Phone validation
            else if (fieldName === 'phone' && value && !FreshShop.utils.isValidPhone(value)) {
                isValid = false;
                message = 'Số điện thoại không hợp lệ';
            }
            // Password confirmation
            else if (fieldName === 'pwconfirm') {
                const passwordField = document.querySelector('input[name="password"]');
                if (passwordField && value !== passwordField.value) {
                    isValid = false;
                    message = 'Mật khẩu xác nhận không khớp';
                }
            }

            // Apply validation styling
            if (value) { // Only show validation for non-empty fields
                field.classList.add(isValid ? 'is-valid' : 'is-invalid');
                
                if (!isValid) {
                    const feedback = document.createElement('div');
                    feedback.className = 'invalid-feedback';
                    feedback.textContent = message;
                    field.parentNode.appendChild(feedback);
                }
            }

            return isValid;
        }
    },

    // Initialize the module
    init() {
        console.log('FreshShop module initialized');
        
        // Setup real-time validation for common forms
        this.validation.setupRealTimeValidation('#loginForm');
        this.validation.setupRealTimeValidation('#registerForm');
        this.validation.setupRealTimeValidation('#checkoutForm');
        
        // Update cart badge on page load
        this.cart.updateBadge();
        
        // Setup global error handling
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => FreshShop.init());
} else {
    FreshShop.init();
}

// Make FreshShop globally available
window.FreshShop = FreshShop;
