/**
 * FreshShop JavaScript Utilities
 * Optimized and modernized version
 */

// Main sorting function with error handling
function sort() {
    try {
        const combobox = document.getElementById("sort");
        if (!combobox) {
            console.warn('Sort combobox not found');
            return;
        }

        const value = combobox.value;
        const sortFunctions = {
            "1": () => sortOrderByPrice(true),  // ASC
            "2": () => sortOrderByPrice(false), // DESC
            "3": () => sortOrderByStatus("mới"),
            "4": () => sortOrderByStatus("giảm giá")
        };

        const sortFunction = sortFunctions[value];
        if (sortFunction) {
            sortFunction();
        }
    } catch (error) {
        console.error('Error in sort function:', error);
    }
}

// Optimized product list extraction with caching
let cachedElements = null;

function getProductElements() {
    if (!cachedElements) {
        cachedElements = {
            id: document.getElementsByClassName("id-param"),
            name: document.getElementsByClassName("name-param"),
            price: document.getElementsByClassName("price-param"),
            image: document.getElementsByClassName("image-param"),
            status: document.getElementsByClassName("status-param")
        };
    }
    return cachedElements;
}

function getList() {
    try {
        const elements = getProductElements();
        const length = elements.id.length;

        if (length === 0) {
            console.warn('No products found to sort');
            return [];
        }

        const list = [];
        for (let i = 0; i < length; i++) {
            const product = {
                id: elements.id[i]?.getAttribute("href") || "",
                name: elements.name[i]?.innerHTML || "",
                price: elements.price[i]?.innerHTML || "0",
                image: elements.image[i]?.getAttribute("src") || "",
                status: elements.status[i]?.innerHTML || ""
            };
            list.push(product);
        }
        return list;
    } catch (error) {
        console.error('Error in getList:', error);
        return [];
    }
}

// Optimized DOM update function
function transform(list) {
    try {
        const elements = getProductElements();
        const length = Math.min(list.length, elements.id.length);

        // Use document fragment for better performance
        for (let i = 0; i < length; i++) {
            const product = list[i];
            if (elements.id[i]) elements.id[i].setAttribute("href", product.id);
            if (elements.name[i]) elements.name[i].innerHTML = product.name;
            if (elements.price[i]) elements.price[i].innerHTML = product.price;
            if (elements.image[i]) elements.image[i].setAttribute("src", product.image);
            if (elements.status[i]) elements.status[i].innerHTML = product.status;
        }
    } catch (error) {
        console.error('Error in transform:', error);
    }
}

// Unified sorting functions
function sortOrderByPrice(ascending = true) {
    try {
        const list = getList();
        if (list.length === 0) return;

        const sortedList = list.sort((a, b) => {
            const priceA = parseFloat(a.price.replace(/[^\d.-]/g, '')) || 0;
            const priceB = parseFloat(b.price.replace(/[^\d.-]/g, '')) || 0;
            return ascending ? priceA - priceB : priceB - priceA;
        });

        transform(sortedList);
    } catch (error) {
        console.error('Error in sortOrderByPrice:', error);
    }
}

function sortOrderByStatus(targetStatus) {
    try {
        const list = getList();
        if (list.length === 0) return;

        const sortedList = list.sort((a, b) => {
            const aHasStatus = a.status.toLowerCase().includes(targetStatus.toLowerCase());
            const bHasStatus = b.status.toLowerCase().includes(targetStatus.toLowerCase());

            if (aHasStatus && !bHasStatus) return -1;
            if (!aHasStatus && bHasStatus) return 1;
            return 0;
        });

        transform(sortedList);
    } catch (error) {
        console.error('Error in sortOrderByStatus:', error);
    }
}

// Legacy functions removed - replaced with modern efficient sorting above

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Enhanced Cart functionality with better error handling
function updateCart(productId, quantity = 1, cumulative = true) {
    // Validate inputs
    if (!productId || quantity <= 0) {
        showNotification('Thông tin sản phẩm không hợp lệ', 'danger');
        return Promise.reject('Invalid product data');
    }

    // Show loading state
    const loadingBtn = document.querySelector(`[data-product-id="${productId}"]`);
    if (loadingBtn) {
        loadingBtn.disabled = true;
        loadingBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> Đang thêm...';
    }

    return fetch('/add-item', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `idP=${productId}&quan=${quantity}&cumulative=${cumulative}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    })
    .then(data => {
        console.log('Cart updated:', data);
        showNotification('Đã thêm sản phẩm vào giỏ hàng', 'success');

        // Update cart badge and dropdown if visible
        updateCartBadge();
        if (typeof cartDropdownVisible !== 'undefined' && cartDropdownVisible) {
            loadCartItems();
        }
        return data;
    })
    .catch(error => {
        console.error('Error updating cart:', error);
    });
}

function removeFromCart(productId) {
    fetch('/remove-item-cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `idP=${productId}`
    })
    .then(response => response.text())
    .then(data => {
        console.log('Item removed from cart:', data);
        location.reload(); // Reload page to update cart display
    })
    .catch(error => {
        console.error('Error removing item from cart:', error);
    });
}

function clearCart() {
    if (!confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm khỏi giỏ hàng?')) {
        return;
    }

    fetch('/clear-cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        }
    })
    .then(response => response.text())
    .then(data => {
        // Update cart dropdown if visible
        if (cartDropdownVisible) {
            loadCartItems();
        }
        updateCartBadge();
        showCartMessage('Đã xóa tất cả sản phẩm khỏi giỏ hàng!', 'success');
    })
    .catch(error => {
        showCartMessage('Có lỗi xảy ra khi xóa giỏ hàng!', 'error');
    });
}

function updateCartBadge() {
    // Fetch current cart count from server
    fetch('/cart-count', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        // Update cart badge in header
        const cartBadge = document.querySelector('#cart-badge');
        if (cartBadge && data.count !== undefined) {
            cartBadge.textContent = data.count;
        }

        // Also update the old badge selector for backward compatibility
        const oldCartBadge = document.querySelector('.attr-nav .badge');
        if (oldCartBadge && data.count !== undefined) {
            oldCartBadge.textContent = data.count;
        }
    })
    .catch(error => {
        console.error('Error updating cart badge:', error);
    });
}

// Quick add to cart function for shop pages
function quickAddToCart(productId, quantity = 1) {
    updateCart(productId, quantity, true);

    // Show a brief success message
    showCartMessage('Đã thêm vào giỏ hàng!', 'success');
}

// Show cart message function
function showCartMessage(message, type = 'info') {
    // Create message element
    const messageDiv = document.createElement('div');

    // Determine alert class based on type
    let alertClass = 'alert-info';
    let bgColor = '#d1ecf1';
    let textColor = '#0c5460';

    if (type === 'success') {
        alertClass = 'alert-success';
        bgColor = '#d4edda';
        textColor = '#155724';
    } else if (type === 'error') {
        alertClass = 'alert-danger';
        bgColor = '#f8d7da';
        textColor = '#721c24';
    }

    messageDiv.className = `alert ${alertClass} cart-message`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        padding: 12px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        background-color: ${bgColor};
        color: ${textColor};
        border: 1px solid ${textColor}33;
        min-width: 250px;
        font-weight: 500;
        animation: slideInRight 0.3s ease-out;
        font-family: Arial, sans-serif;
        font-size: 14px;
        line-height: 1.4;
    `;
    messageDiv.innerHTML = `<strong>${type === 'success' ? 'Thành công!' : type === 'error' ? 'Lỗi!' : 'Thông báo!'}</strong> ${message}`;

    // Add to page
    document.body.appendChild(messageDiv);

    // Remove after 3 seconds
    setTimeout(() => {
        messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 300);
    }, 3000);
}

// Cart dropdown functionality
let cartDropdownVisible = false;

function toggleCartDropdown() {
    if (cartDropdownVisible) {
        hideCartDropdown();
    } else {
        showCartDropdown();
    }
}

// Cart dropdown functionality
function showCartDropdown() {
    const dropdown = document.getElementById('cart-dropdown');
    if (dropdown) {
        dropdown.style.display = 'block';
        cartDropdownVisible = true;
    }
}

function hideCartDropdown() {
    const dropdown = document.getElementById('cart-dropdown');
    if (dropdown) {
        dropdown.style.display = 'none';
        cartDropdownVisible = false;
    }
}

function toggleCartDropdown() {
    if (cartDropdownVisible) {
        hideCartDropdown();
    } else {
        showCartDropdown();
    }
}

function hideCartDropdown() {
    const dropdown = document.getElementById('cart-dropdown');
    if (!dropdown) return;

    console.log('Hiding cart dropdown...');

    cartDropdownVisible = false;

    // Hide dropdown with simple inline style override
    dropdown.style.display = 'none';
    dropdown.style.opacity = '0';
    dropdown.style.transform = 'translateY(-10px)';
    dropdown.style.pointerEvents = 'none';
    dropdown.style.visibility = 'hidden';

    document.removeEventListener('click', handleOutsideClick);
}

function handleOutsideClick(event) {
    const cartDropdown = document.querySelector('.cart-dropdown');
    if (cartDropdown && !cartDropdown.contains(event.target)) {
        hideCartDropdown();
    }
}

function loadCartItems() {
    const container = document.getElementById('cart-items-container');
    const loading = document.getElementById('cart-loading');

    if (!container) {
        return;
    }

    // Show loading
    if (loading) {
        loading.style.display = 'block';
    }

    // Fetch cart items
    fetch('/api/cart/items', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (loading) {
            loading.style.display = 'none';
        }
        renderCartItems(data);
    })
    .catch(error => {
        if (loading) {
            loading.style.display = 'none';
        }
        if (container) {
            container.innerHTML = '<div class="cart-empty">Có lỗi xảy ra khi tải giỏ hàng</div>';
        }
    });
}

function renderCartItems(data) {
    const container = document.getElementById('cart-items-container');
    const totalElement = document.getElementById('cart-total-amount');
    const countElement = document.getElementById('cart-count-text');

    if (!container) {
        return;
    }

    if (!data.items || data.items.length === 0) {
        container.innerHTML = '<div class="cart-empty">Giỏ hàng trống</div>';
        if (totalElement) {
            totalElement.textContent = '0đ';
        }
        if (countElement) {
            countElement.textContent = '0 sản phẩm';
        }
        return;
    }

    // Render cart items
    const itemsHtml = data.items.map(item => {
        const product = item.product;
        const imageUrl = (product.images && product.images.length > 0)
            ? product.images[0].url
            : '/images/Tao Fuji Newzealand - 1kg.jpg';

        const price = product.discountedPrice > 0 && product.discountedPrice !== product.price
            ? product.discountedPrice
            : product.price;

        return `
            <div class="cart-item">
                <img src="${imageUrl}" alt="${product.name}" class="cart-item-image">
                <div class="cart-item-details">
                    <div class="cart-item-name">${product.name}</div>
                    <div class="cart-item-price">${price}đ</div>
                    <div class="cart-item-quantity">Số lượng: ${item.quantity}</div>
                </div>
                <button class="cart-item-remove" onclick="removeFromCartDropdown(${product.id})" title="Xóa">
                    <i class="fa fa-times"></i>
                </button>
            </div>
        `;
    }).join('');

    container.innerHTML = itemsHtml;
    if (totalElement) {
        totalElement.textContent = data.total + 'đ';
    }
    if (countElement) {
        countElement.textContent = data.items.length + ' sản phẩm';
    }
}

function removeFromCartDropdown(productId) {
    fetch('/remove-item-cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `idP=${productId}`
    })
    .then(response => response.text())
    .then(data => {
        // Reload cart items
        loadCartItems();
        updateCartBadge();
        showCartMessage('Đã xóa sản phẩm khỏi giỏ hàng!', 'success');
    })
    .catch(error => {
        showCartMessage('Có lỗi xảy ra khi xóa sản phẩm!', 'error');
    });
}

// Initialize cart functionality on page load
document.addEventListener('DOMContentLoaded', function() {
    updateCartBadge();

    // Initialize cart dropdown elements
    const cartDropdown = document.getElementById('cart-dropdown');
    const cartToggle = document.getElementById('cart-toggle');

    if (cartDropdown && cartToggle) {
        cartDropdownVisible = false;
        cartDropdown.style.display = 'none';
    }
});