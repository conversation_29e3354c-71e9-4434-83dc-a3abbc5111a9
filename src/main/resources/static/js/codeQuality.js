/**
 * Code Quality and Performance Monitoring
 * FreshShop Application
 */

const CodeQuality = {
    // Performance monitoring
    performance: {
        marks: new Map(),
        
        // Mark performance points
        mark(name) {
            if (window.performance && window.performance.mark) {
                window.performance.mark(name);
                this.marks.set(name, Date.now());
            }
        },
        
        // Measure performance between marks
        measure(name, startMark, endMark) {
            if (window.performance && window.performance.measure) {
                try {
                    window.performance.measure(name, startMark, endMark);
                    const measure = window.performance.getEntriesByName(name)[0];
                    console.log(`Performance: ${name} took ${measure.duration.toFixed(2)}ms`);
                    return measure.duration;
                } catch (error) {
                    console.warn('Performance measurement failed:', error);
                }
            }
            return null;
        },
        
        // Monitor page load performance
        monitorPageLoad() {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    if (window.performance && window.performance.timing) {
                        const timing = window.performance.timing;
                        const loadTime = timing.loadEventEnd - timing.navigationStart;
                        const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
                        
                        console.log(`Page Load Performance:
                            - Total Load Time: ${loadTime}ms
                            - DOM Ready: ${domReady}ms
                            - DNS Lookup: ${timing.domainLookupEnd - timing.domainLookupStart}ms
                            - Server Response: ${timing.responseEnd - timing.requestStart}ms
                        `);
                        
                        // Send to analytics if needed
                        this.reportPerformance({
                            loadTime,
                            domReady,
                            dnsTime: timing.domainLookupEnd - timing.domainLookupStart,
                            serverTime: timing.responseEnd - timing.requestStart
                        });
                    }
                }, 100);
            });
        },
        
        // Report performance metrics
        reportPerformance(metrics) {
            // Could send to analytics service
            if (metrics.loadTime > 3000) {
                console.warn('Page load time is slow:', metrics.loadTime + 'ms');
            }
        }
    },
    
    // Error tracking
    errorTracking: {
        errors: [],
        
        // Track JavaScript errors
        trackErrors() {
            window.addEventListener('error', (event) => {
                const error = {
                    message: event.message,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    error: event.error,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };
                
                this.errors.push(error);
                this.reportError(error);
            });
            
            // Track unhandled promise rejections
            window.addEventListener('unhandledrejection', (event) => {
                const error = {
                    type: 'unhandledrejection',
                    reason: event.reason,
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };
                
                this.errors.push(error);
                this.reportError(error);
            });
        },
        
        // Report errors
        reportError(error) {
            console.error('Application Error:', error);
            
            // Could send to error tracking service
            // Example: Sentry, LogRocket, etc.
        },
        
        // Get error summary
        getErrorSummary() {
            return {
                totalErrors: this.errors.length,
                recentErrors: this.errors.slice(-5),
                errorTypes: this.errors.reduce((acc, error) => {
                    const type = error.type || 'javascript';
                    acc[type] = (acc[type] || 0) + 1;
                    return acc;
                }, {})
            };
        }
    },
    
    // Accessibility monitoring
    accessibility: {
        // Check for common accessibility issues
        checkAccessibility() {
            const issues = [];
            
            // Check for images without alt text
            const imagesWithoutAlt = document.querySelectorAll('img:not([alt])');
            if (imagesWithoutAlt.length > 0) {
                issues.push(`${imagesWithoutAlt.length} images missing alt text`);
            }
            
            // Check for buttons without accessible names
            const buttonsWithoutNames = document.querySelectorAll('button:not([aria-label]):not([title])');
            buttonsWithoutNames.forEach(button => {
                if (!button.textContent.trim()) {
                    issues.push('Button without accessible name found');
                }
            });
            
            // Check for form inputs without labels
            const inputsWithoutLabels = document.querySelectorAll('input:not([aria-label]):not([aria-labelledby])');
            inputsWithoutLabels.forEach(input => {
                const label = document.querySelector(`label[for="${input.id}"]`);
                if (!label && input.type !== 'hidden') {
                    issues.push(`Input without label: ${input.name || input.type}`);
                }
            });
            
            // Check color contrast (basic check)
            this.checkColorContrast();
            
            if (issues.length > 0) {
                console.warn('Accessibility Issues Found:', issues);
            } else {
                console.log('No obvious accessibility issues found');
            }
            
            return issues;
        },
        
        // Basic color contrast check
        checkColorContrast() {
            // This is a simplified check - in production, use a proper contrast checking library
            const elements = document.querySelectorAll('*');
            elements.forEach(element => {
                const styles = window.getComputedStyle(element);
                const color = styles.color;
                const backgroundColor = styles.backgroundColor;
                
                // Basic check for very light text on light background
                if (color === 'rgb(255, 255, 255)' && backgroundColor === 'rgb(255, 255, 255)') {
                    console.warn('Potential contrast issue:', element);
                }
            });
        }
    },
    
    // Code quality checks
    codeQuality: {
        // Check for common code quality issues
        checkCodeQuality() {
            const issues = [];
            
            // Check for inline styles
            const elementsWithInlineStyles = document.querySelectorAll('[style]');
            if (elementsWithInlineStyles.length > 0) {
                issues.push(`${elementsWithInlineStyles.length} elements with inline styles`);
            }
            
            // Check for inline event handlers
            const elementsWithInlineEvents = document.querySelectorAll('[onclick], [onload], [onchange]');
            if (elementsWithInlineEvents.length > 0) {
                issues.push(`${elementsWithInlineEvents.length} elements with inline event handlers`);
            }
            
            // Check for deprecated HTML elements
            const deprecatedElements = document.querySelectorAll('font, center, big, small');
            if (deprecatedElements.length > 0) {
                issues.push(`${deprecatedElements.length} deprecated HTML elements found`);
            }
            
            if (issues.length > 0) {
                console.warn('Code Quality Issues:', issues);
            }
            
            return issues;
        }
    },
    
    // Initialize all monitoring
    init() {
        console.log('Code Quality monitoring initialized');
        
        // Start performance monitoring
        this.performance.monitorPageLoad();
        
        // Start error tracking
        this.errorTracking.trackErrors();
        
        // Run accessibility check after page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                this.accessibility.checkAccessibility();
                this.codeQuality.checkCodeQuality();
            }, 1000);
        });
        
        // Expose methods for debugging
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            window.CodeQuality = this;
            console.log('CodeQuality object exposed for debugging');
        }
    }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => CodeQuality.init());
} else {
    CodeQuality.init();
}
