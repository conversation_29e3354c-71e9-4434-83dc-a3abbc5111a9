/**
 * FreshShop Performance Optimizations
 * Critical CSS and performance improvements
 */

/* Performance optimizations */
* {
    box-sizing: border-box;
}

/* Optimize font loading */
@font-face {
    font-display: swap; /* Improve font loading performance */
}

/* Lazy loading images */
img {
    loading: lazy;
    transition: opacity 0.3s ease;
}

img[data-src] {
    opacity: 0;
}

img[data-loaded="true"] {
    opacity: 1;
}

/* Optimize animations */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button loading states */
.btn.loading {
    color: transparent;
}

.btn.loading::after {
    color: white;
    border-color: rgba(255,255,255,0.3);
    border-top-color: white;
}

/* Notification improvements */
.fresh-notification {
    backdrop-filter: blur(10px);
    border: none;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.fresh-notification .fa {
    margin-right: 8px;
}

/* Form validation improvements */
.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
}

/* Cart badge improvements */
.cart-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Responsive improvements */
@media (max-width: 768px) {
    .fresh-notification {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
    
    .btn.loading::after {
        width: 16px;
        height: 16px;
        margin: -8px 0 0 -8px;
    }
}

/* Accessibility improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus improvements */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Optimize table performance */
.table {
    table-layout: fixed;
}

/* Optimize grid layouts */
.row {
    display: flex;
    flex-wrap: wrap;
}

/* Critical path CSS for above-the-fold content */
.header,
.navbar,
.hero-section {
    contain: layout style paint;
}

/* Optimize background images */
.bg-image {
    background-attachment: scroll; /* Better performance than fixed */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

/* Optimize transforms */
.transform-gpu {
    transform: translateZ(0);
    will-change: transform;
}

/* Optimize opacity changes */
.opacity-transition {
    will-change: opacity;
    transition: opacity 0.3s ease;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .btn,
    .navbar,
    .footer {
        display: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .fresh-notification {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Performance monitoring */
.perf-mark {
    display: none;
}

/* Optimize list rendering */
.product-list {
    contain: layout style paint;
}

.product-item {
    contain: layout style;
}

/* Optimize modal performance */
.modal {
    contain: layout style paint;
}

/* Optimize dropdown performance */
.dropdown-menu {
    contain: layout style paint;
    transform: translateZ(0);
}

/* Critical CSS end marker */
.critical-css-loaded {
    display: none;
}
