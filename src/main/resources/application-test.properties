# Test Environment Configuration
# FreshShop Application

# Database configuration for testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA and Hibernate settings for testing
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.open-in-view=false
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# H2 Console for testing
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Hibernate performance settings for testing
spring.jpa.properties.hibernate.jdbc.batch_size=10
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.generate_statistics=false

# Connection pool configuration for testing
spring.datasource.hikari.minimum-idle=1
spring.datasource.hikari.maximum-pool-size=5
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariPool-FreshShop-Test
spring.datasource.hikari.max-lifetime=600000
spring.datasource.hikari.connection-timeout=10000

# Logging configuration for testing
logging.level.vn.fshop=DEBUG
logging.level.org.springframework.security=INFO
logging.level.org.springframework.web=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.test=DEBUG
logging.level.org.springframework.boot.test=DEBUG

# Console logging pattern for testing
logging.pattern.console=%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# Server settings for testing
server.port=0
server.error.include-message=always
server.error.include-binding-errors=always
server.error.include-stacktrace=always
server.error.include-exception=true

# Session configuration for testing
server.servlet.session.timeout=10m
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false

# File upload settings for testing
spring.servlet.multipart.max-file-size=1MB
spring.servlet.multipart.max-request-size=1MB
spring.servlet.multipart.enabled=true

# Cache configuration for testing
spring.cache.type=simple
spring.cache.cache-names=categories,products,productsByCategory,productImages,userProfiles,productStats,orderStats

# Actuator endpoints for testing
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.show-components=always

# Application info for testing
info.app.name=FreshShop
info.app.description=Fresh Fruit E-commerce Application - Test Environment
info.app.version=@project.version@
info.app.environment=test

# Security settings for testing
spring.security.debug=false

# Thymeleaf settings for testing
spring.thymeleaf.cache=false
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html

# Static resources for testing
spring.web.resources.cache.period=0
spring.web.resources.chain.cache=false

# Test specific settings
spring.test.database.replace=none
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=always

# Disable unnecessary features for testing
spring.devtools.restart.enabled=false
spring.devtools.livereload.enabled=false

# Test data initialization
spring.sql.init.data-locations=classpath:data-test.sql
spring.sql.init.schema-locations=classpath:schema-test.sql
spring.sql.init.continue-on-error=false

# Transaction settings for testing
spring.jpa.properties.hibernate.connection.autocommit=false
spring.transaction.default-timeout=30

# Async settings for testing
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=4
spring.task.scheduling.pool.size=2
