package vn.fshop.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.metrics.MetricsEndpoint;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;

/**
 * Monitoring and Metrics Configuration
 * Provides comprehensive monitoring for the FreshShop application
 */
@Configuration
public class MonitoringConfig {

    @Autowired
    private MeterRegistry meterRegistry;

    /**
     * Custom metrics for business operations
     */
    @Bean
    public Counter userRegistrationCounter() {
        return Counter.builder("freshShop.user.registrations")
                .description("Number of user registrations")
                .register(meterRegistry);
    }

    @Bean
    public Counter orderCreationCounter() {
        return Counter.builder("freshShop.orders.created")
                .description("Number of orders created")
                .register(meterRegistry);
    }

    @Bean
    public Counter productViewCounter() {
        return Counter.builder("freshShop.products.views")
                .description("Number of product views")
                .register(meterRegistry);
    }

    @Bean
    public Counter cartAdditionCounter() {
        return Counter.builder("freshShop.cart.additions")
                .description("Number of items added to cart")
                .register(meterRegistry);
    }

    @Bean
    public Timer orderProcessingTimer() {
        return Timer.builder("freshShop.orders.processing.time")
                .description("Time taken to process orders")
                .register(meterRegistry);
    }

    @Bean
    public Timer databaseQueryTimer() {
        return Timer.builder("freshShop.database.query.time")
                .description("Time taken for database queries")
                .register(meterRegistry);
    }

    /**
     * Custom request monitoring filter
     */
    @Bean
    public OncePerRequestFilter requestMonitoringFilter() {
        return new OncePerRequestFilter() {
            private final Counter requestCounter = Counter.builder("freshShop.http.requests")
                    .description("HTTP requests")
                    .register(meterRegistry);

            private final Timer requestTimer = Timer.builder("freshShop.http.request.duration")
                    .description("HTTP request duration")
                    .register(meterRegistry);

            @Override
            protected void doFilterInternal(HttpServletRequest request, 
                                          HttpServletResponse response, 
                                          FilterChain filterChain) throws ServletException, IOException {
                
                Timer.Sample sample = Timer.start(meterRegistry);
                String method = request.getMethod();
                String uri = request.getRequestURI();
                
                try {
                    filterChain.doFilter(request, response);
                } finally {
                    sample.stop(requestTimer);
                    
                    requestCounter.increment(
                        "method", method,
                        "uri", sanitizeUri(uri),
                        "status", String.valueOf(response.getStatus())
                    );
                }
            }

            private String sanitizeUri(String uri) {
                // Replace dynamic path segments with placeholders
                return uri.replaceAll("/\\d+", "/{id}")
                         .replaceAll("/[a-f0-9-]{36}", "/{uuid}")
                         .replaceAll("/\\w+@\\w+\\.\\w+", "/{email}");
            }
        };
    }

    /**
     * Business metrics service
     */
    @Bean
    public BusinessMetricsService businessMetricsService() {
        return new BusinessMetricsService(meterRegistry);
    }

    /**
     * Service for tracking business-specific metrics
     */
    public static class BusinessMetricsService {
        private final MeterRegistry meterRegistry;
        private final Counter userRegistrations;
        private final Counter orderCreations;
        private final Counter productViews;
        private final Counter cartAdditions;
        private final Timer orderProcessingTime;

        public BusinessMetricsService(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            this.userRegistrations = Counter.builder("freshShop.business.user.registrations")
                    .register(meterRegistry);
            this.orderCreations = Counter.builder("freshShop.business.orders.created")
                    .register(meterRegistry);
            this.productViews = Counter.builder("freshShop.business.products.views")
                    .register(meterRegistry);
            this.cartAdditions = Counter.builder("freshShop.business.cart.additions")
                    .register(meterRegistry);
            this.orderProcessingTime = Timer.builder("freshShop.business.orders.processing.time")
                    .register(meterRegistry);
        }

        public void recordUserRegistration() {
            userRegistrations.increment();
        }

        public void recordOrderCreation(double amount) {
            orderCreations.increment("amount_range", getAmountRange(amount));
        }

        public void recordProductView(String category) {
            productViews.increment("category", category);
        }

        public void recordCartAddition(String productCategory) {
            cartAdditions.increment("category", productCategory);
        }

        public Timer.Sample startOrderProcessing() {
            return Timer.start(meterRegistry);
        }

        public void recordOrderProcessingTime(Timer.Sample sample) {
            sample.stop(orderProcessingTime);
        }

        private String getAmountRange(double amount) {
            if (amount < 100000) return "under_100k";
            if (amount < 500000) return "100k_500k";
            if (amount < 1000000) return "500k_1m";
            return "over_1m";
        }
    }

    /**
     * Performance monitoring configuration
     */
    @Bean
    public PerformanceMonitor performanceMonitor() {
        return new PerformanceMonitor(meterRegistry);
    }

    /**
     * Performance monitoring service
     */
    public static class PerformanceMonitor {
        private final MeterRegistry meterRegistry;
        private final Timer cacheAccessTime;
        private final Counter cacheHits;
        private final Counter cacheMisses;

        public PerformanceMonitor(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            this.cacheAccessTime = Timer.builder("freshShop.cache.access.time")
                    .register(meterRegistry);
            this.cacheHits = Counter.builder("freshShop.cache.hits")
                    .register(meterRegistry);
            this.cacheMisses = Counter.builder("freshShop.cache.misses")
                    .register(meterRegistry);
        }

        public void recordCacheHit(String cacheName) {
            cacheHits.increment("cache", cacheName);
        }

        public void recordCacheMiss(String cacheName) {
            cacheMisses.increment("cache", cacheName);
        }

        public Timer.Sample startCacheAccess() {
            return Timer.start(meterRegistry);
        }

        public void recordCacheAccessTime(Timer.Sample sample, String cacheName) {
            sample.stop(Timer.builder("freshShop.cache.access.time")
                    .tag("cache", cacheName)
                    .register(meterRegistry));
        }
    }
}
