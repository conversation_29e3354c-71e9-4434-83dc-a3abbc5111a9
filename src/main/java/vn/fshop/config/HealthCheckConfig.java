package vn.fshop.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.boot.actuator.info.InfoContributor;
import org.springframework.boot.actuator.info.Info;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import vn.fshop.repository.UserRepository;
import vn.fshop.repository.ProductRepository;
import vn.fshop.repository.OrderRepository;

import javax.sql.DataSource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Health Check Configuration for FreshShop Application
 * Provides comprehensive health monitoring for all critical components
 */
@Configuration
public class HealthCheckConfig {

    @Autowired
    private DataSource dataSource;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ProductRepository productRepository;

    @Autowired
    private OrderRepository orderRepository;

    /**
     * Database connectivity health check
     */
    @Bean
    public HealthIndicator databaseHealthIndicator() {
        return () -> {
            try {
                JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
                jdbcTemplate.queryForObject("SELECT 1", Integer.class);
                
                return Health.up()
                    .withDetail("database", "MySQL")
                    .withDetail("status", "Connected")
                    .withDetail("timestamp", LocalDateTime.now())
                    .build();
            } catch (Exception e) {
                return Health.down()
                    .withDetail("database", "MySQL")
                    .withDetail("status", "Connection failed")
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", LocalDateTime.now())
                    .build();
            }
        };
    }

    /**
     * Application data health check
     */
    @Bean
    public HealthIndicator applicationDataHealthIndicator() {
        return () -> {
            try {
                // Check if essential data exists
                long userCount = userRepository.count();
                long productCount = productRepository.count();
                long orderCount = orderRepository.count();
                
                Map<String, Object> details = new HashMap<>();
                details.put("users", userCount);
                details.put("products", productCount);
                details.put("orders", orderCount);
                details.put("timestamp", LocalDateTime.now());
                
                // Consider unhealthy if no products exist
                if (productCount == 0) {
                    return Health.down()
                        .withDetail("reason", "No products found in database")
                        .withDetails(details)
                        .build();
                }
                
                return Health.up()
                    .withDetails(details)
                    .build();
                    
            } catch (Exception e) {
                return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("timestamp", LocalDateTime.now())
                    .build();
            }
        };
    }

    /**
     * Memory usage health check
     */
    @Bean
    public HealthIndicator memoryHealthIndicator() {
        return () -> {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            double memoryUsagePercentage = (double) usedMemory / maxMemory * 100;
            
            Map<String, Object> details = new HashMap<>();
            details.put("max", formatBytes(maxMemory));
            details.put("total", formatBytes(totalMemory));
            details.put("used", formatBytes(usedMemory));
            details.put("free", formatBytes(freeMemory));
            details.put("usage_percentage", String.format("%.2f%%", memoryUsagePercentage));
            details.put("timestamp", LocalDateTime.now());
            
            if (memoryUsagePercentage > 90) {
                return Health.down()
                    .withDetail("reason", "High memory usage")
                    .withDetails(details)
                    .build();
            } else if (memoryUsagePercentage > 80) {
                return Health.up()
                    .withDetail("warning", "Memory usage is high")
                    .withDetails(details)
                    .build();
            } else {
                return Health.up()
                    .withDetails(details)
                    .build();
            }
        };
    }

    /**
     * Application info contributor
     */
    @Bean
    public InfoContributor applicationInfoContributor() {
        return builder -> {
            Map<String, Object> appInfo = new HashMap<>();
            appInfo.put("name", "FreshShop");
            appInfo.put("description", "Fresh Fruit E-commerce Application");
            appInfo.put("startup_time", LocalDateTime.now());
            
            // Runtime information
            Map<String, Object> runtime = new HashMap<>();
            runtime.put("java_version", System.getProperty("java.version"));
            runtime.put("java_vendor", System.getProperty("java.vendor"));
            runtime.put("os_name", System.getProperty("os.name"));
            runtime.put("os_version", System.getProperty("os.version"));
            runtime.put("processors", Runtime.getRuntime().availableProcessors());
            
            // Application statistics
            try {
                Map<String, Object> stats = new HashMap<>();
                stats.put("total_users", userRepository.count());
                stats.put("total_products", productRepository.count());
                stats.put("total_orders", orderRepository.count());
                appInfo.put("statistics", stats);
            } catch (Exception e) {
                appInfo.put("statistics_error", "Unable to fetch statistics: " + e.getMessage());
            }
            
            builder.withDetail("application", appInfo);
            builder.withDetail("runtime", runtime);
        };
    }

    /**
     * Custom health check for external dependencies
     */
    @Bean
    public HealthIndicator externalDependenciesHealthIndicator() {
        return () -> {
            Map<String, Object> details = new HashMap<>();
            
            // Check file system access
            try {
                java.io.File tempFile = java.io.File.createTempFile("health", "check");
                tempFile.delete();
                details.put("file_system", "accessible");
            } catch (Exception e) {
                details.put("file_system", "error: " + e.getMessage());
                return Health.down()
                    .withDetail("reason", "File system access failed")
                    .withDetails(details)
                    .build();
            }
            
            // Check static resources directory
            try {
                java.io.File staticDir = new java.io.File("src/main/resources/static");
                details.put("static_resources", staticDir.exists() ? "accessible" : "not found");
            } catch (Exception e) {
                details.put("static_resources", "error: " + e.getMessage());
            }
            
            details.put("timestamp", LocalDateTime.now());
            
            return Health.up()
                .withDetails(details)
                .build();
        };
    }

    /**
     * Format bytes to human readable format
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp - 1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }
}
