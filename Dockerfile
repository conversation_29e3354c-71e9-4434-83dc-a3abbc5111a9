# Multi-stage Docker build for FreshShop Application
# Optimized for production deployment

# Build stage
FROM maven:3.9.6-eclipse-temurin-21 AS build

# Set working directory
WORKDIR /app

# Copy pom.xml first for better layer caching
COPY pom.xml .

# Download dependencies (this layer will be cached if pom.xml doesn't change)
RUN mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application
RUN mvn clean package -DskipTests -B

# Runtime stage
FROM eclipse-temurin:21-jre-alpine

# Install necessary packages
RUN apk add --no-cache \
    curl \
    tzdata \
    && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application user for security
RUN addgroup -g 1001 -S freshShop && \
    adduser -u 1001 -S freshShop -G freshShop

# Create application directory
WORKDIR /app

# Create logs directory
RUN mkdir -p /app/logs && \
    chown -R freshShop:freshShop /app

# Copy the built JAR from build stage
COPY --from=build /app/target/*.jar app.jar

# Copy static resources if needed
COPY --from=build /app/src/main/resources/static ./static

# Change ownership to application user
RUN chown -R freshShop:freshShop /app

# Switch to application user
USER freshShop

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# Environment variables with defaults
ENV SPRING_PROFILES_ACTIVE=prod
ENV JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC -XX:+UseStringDeduplication"
ENV SERVER_PORT=8080

# JVM options for production
ENV JAVA_TOOL_OPTIONS="-XX:+ExitOnOutOfMemoryError -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/app/logs/"

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS $JAVA_TOOL_OPTIONS -jar app.jar"]

# Labels for metadata
LABEL maintainer="FreshShop Team"
LABEL version="1.0"
LABEL description="FreshShop - Fresh Fruit E-commerce Application"
