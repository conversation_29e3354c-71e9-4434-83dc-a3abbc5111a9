#!/bin/bash

# FreshShop Deployment Script
# Automated deployment script for production environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_NAME="FreshShop"
DOCKER_IMAGE="freshShop:latest"
BACKUP_DIR="/backup/freshShop"
LOG_FILE="/var/log/freshShop/deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed"
    fi
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        warning ".env file not found. Using default values."
        if [ -f ".env.example" ]; then
            log "Copying .env.example to .env"
            cp .env.example .env
        fi
    fi
    
    success "Prerequisites check completed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker-compose ps mysql | grep -q "Up"; then
        log "Backing up database..."
        docker-compose exec -T mysql mysqldump -u root -p"${DB_ROOT_PASSWORD:-freshShop123}" "${DB_NAME:-freshShop_prod}" > "$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        success "Database backup completed"
    else
        warning "MySQL container is not running, skipping database backup"
    fi
    
    # Backup application logs
    if [ -d "logs" ]; then
        log "Backing up application logs..."
        tar -czf "$BACKUP_DIR/logs_backup_$(date +%Y%m%d_%H%M%S).tar.gz" logs/
        success "Logs backup completed"
    fi
}

# Build application
build_application() {
    log "Building application..."
    
    # Clean and build with Maven
    if command -v mvn &> /dev/null; then
        log "Building with Maven..."
        mvn clean package -DskipTests
        success "Maven build completed"
    else
        log "Maven not found, using Docker build..."
    fi
    
    # Build Docker image
    log "Building Docker image..."
    docker build -t "$DOCKER_IMAGE" .
    success "Docker image built successfully"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose down
    
    # Start new containers
    log "Starting new containers..."
    docker-compose up -d
    
    # Wait for application to start
    log "Waiting for application to start..."
    sleep 30
    
    # Check health
    check_health
}

# Check application health
check_health() {
    log "Checking application health..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health &> /dev/null; then
            success "Application is healthy"
            return 0
        fi
        
        log "Health check attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    error "Application health check failed after $max_attempts attempts"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    
    # Remove unused containers
    docker container prune -f
    
    # Remove unused images
    docker image prune -f
    
    # Remove old application images (keep last 3)
    docker images "$DOCKER_IMAGE" --format "table {{.Repository}}:{{.Tag}}\t{{.CreatedAt}}" | tail -n +4 | awk '{print $1}' | xargs -r docker rmi
    
    success "Cleanup completed"
}

# Main deployment process
main() {
    log "Starting deployment of $APP_NAME..."
    
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Run deployment steps
    check_prerequisites
    create_backup
    build_application
    deploy_application
    cleanup
    
    success "Deployment completed successfully!"
    log "Application is running at: http://localhost:8080"
    log "Health check: http://localhost:8080/actuator/health"
    log "Logs: docker-compose logs -f app"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        create_backup
        ;;
    "health")
        check_health
        ;;
    "cleanup")
        cleanup
        ;;
    "logs")
        docker-compose logs -f app
        ;;
    "stop")
        log "Stopping application..."
        docker-compose down
        success "Application stopped"
        ;;
    "restart")
        log "Restarting application..."
        docker-compose restart app
        check_health
        success "Application restarted"
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|cleanup|logs|stop|restart}"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Create backup only"
        echo "  health  - Check application health"
        echo "  cleanup - Clean up old Docker resources"
        echo "  logs    - Show application logs"
        echo "  stop    - Stop application"
        echo "  restart - Restart application"
        exit 1
        ;;
esac
